-- 初始化微信小程序导航数据
-- 使用方法：将 {community_id} 替换为实际的小区ID
-- 基于实际数据的完整导航菜单模板，包含父子关系结构

-- 清理可能存在的测试数据（可选）
-- DELETE FROM eh_wx_nav WHERE community_id = '{community_id}';

-- 1. 一级菜单（父菜单）
-- 小区服务 (parent_id = 0)
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
VALUES (0, '{community_id}', '小区服务', '<p><br></p>', '', '', 10, '', '', 0, 0, 1, NOW(), NOW(), 'admin', 'text', 'other', 'indexNav', '', '', NULL, '', 0, '');

-- 信息查询 (parent_id = 0)
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
VALUES (0, '{community_id}', '信息查询', '<p><br></p>', '', '', 20, '', '', 0, 0, 1, NOW(), NOW(), 'admin', 'text', 'other', 'indexNav', '', '', NULL, '', 0, '');

-- 便民服务 (parent_id = 0)
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
VALUES (0, '{community_id}', '便民服务', '<p><br></p>', '', '', 30, '', '', 0, 0, 1, NOW(), NOW(), 'admin', 'text', 'other', 'indexNav', '', '', NULL, '', 0, '');

-- 衣食住行 (parent_id = 0)
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
VALUES (0, '{community_id}', '衣食住行', '<p><br></p>', '', '', 41, 'fire-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'text', 'other', 'indexNav', '', '', NULL, '', 0, '');

-- 2. 小区服务子菜单
-- 小区信息
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '小区信息', '<p><br></p>', '', '', 10, 'wap-home-o', 'goToOcInfo', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 邀请住户
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '邀请住户', '<p><br></p>', '', '', 10, 'friends-o', 'goToVisitor', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 报事报修
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '报事报修', '<p><br></p>', '', '', 11, 'bulb-o', 'goToRepair', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 1, '一键报修'
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 投诉建议
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '投诉建议', '<p><br></p>', '', '', 12, 'edit', 'goToComplaint', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 便民电话
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '便民电话', '<p><br></p>', '', '', 13, 'phone-o', 'goServiceTel', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 联系物业
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '联系物业', '<p><br></p>', '', '', 14, 'phone-circle', 'goToPropertyPhone', 0, 1, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '小区服务' AND parent_id = 0;

-- 3. 信息查询子菜单
-- 缴费公开
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '缴费公开', '<p><br></p>', '', '', 22, 'point-gift-o', 'goToChargeBill', 0, 0, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '信息查询' AND parent_id = 0;

-- 维修基金
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '维修基金', '<p><br></p>', '', '', 23, 'gold-coin-o', '', 0, 1, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx9292b46b82fa2e56","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '信息查询' AND parent_id = 0;

-- 我的医保
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '我的医保', '<p><br></p>', '', '', 24, 'search', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx7ec43a6a6c80544d","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '信息查询' AND parent_id = 0;

-- 挪车查询
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '挪车查询', '<p><br></p>', '', '', 25, 'guide-o', 'goToVehicleRecognize', 0, 0, 1, NOW(), NOW(), 'admin', 'page', 'other', 'indexNav', '', '', NULL, '', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '信息查询' AND parent_id = 0;

-- 4. 便民服务子菜单
-- 生活缴费
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '生活缴费', '<p><br></p>', '', '', 31, 'cash-o', '', 0, 1, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wxd2ade0f25a874ee2","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '便民服务' AND parent_id = 0;

-- 市民热线
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '市民热线', '<p><br></p>', '', '', 32, 'service-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx82ca3e8b5b495f25","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '便民服务' AND parent_id = 0;

-- 公交查询
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '公交查询', '<p><br></p>', '', '', 33, 'guide-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx71d589ea01ce3321","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '便民服务' AND parent_id = 0;

-- 12306购票
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '12306购票', '<p><br></p>', '', '', 34, 'location-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wxa51f55ab3b2655b9","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '便民服务' AND parent_id = 0;

-- 育儿补贴
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '育儿补贴', '<p><br></p>', '', '', 35, 'point-gift-o', '', 0, 1, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx99f1bb712f8494e4","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '便民服务' AND parent_id = 0;

-- 5. 衣食住行子菜单
-- 贝壳找房
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '贝壳找房', '<p><br></p>', '', '', 41, 'home-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wxcfd8224218167d98","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '衣食住行' AND parent_id = 0;

-- 美团
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '美团', '<p><br></p>', '', '', 42, 'shop', '', 0, 1, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wxde8ac0a21135c07d","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '衣食住行' AND parent_id = 0;

-- 滴滴出行
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '滴滴出行', '<p><br></p>', '', '', 43, 'logistics', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wxaf35009675aa0b2a","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '衣食住行' AND parent_id = 0;

-- 同城旅行
INSERT INTO eh_wx_nav (parent_id, community_id, nav_name, content, pdf_file, pdf_file_id, sort, icon_name, tap_name, status, index_show, is_default, create_time, update_time, update_by, nav_type, nav_code, source, pdf_file_list, url, html_content, miniprogram_config, top_show, remark)
SELECT nav_id, '{community_id}', '同城旅行', '<p><br></p>', '', '', 44, 'underway-o', '', 0, 0, 1, NOW(), NOW(), 'admin', 'miniprogram', 'other', 'indexNav', '', '', NULL, '{"appId":"wx336dcaf6a1ecf632","path":""}', 0, ''
FROM eh_wx_nav WHERE community_id = '{community_id}' AND nav_name = '衣食住行' AND parent_id = 0;

-- 使用说明：
-- 1. 将 {community_id} 替换为实际的小区ID
-- 2. 执行SQL时会先创建父菜单，然后通过SELECT子查询获取父菜单的nav_id作为子菜单的parent_id
-- 3. 这样确保了父子关系的正确性，即使nav_id是自增的也能正确关联
-- 4. 所有菜单项都包含完整的字段信息，与原始数据保持一致

