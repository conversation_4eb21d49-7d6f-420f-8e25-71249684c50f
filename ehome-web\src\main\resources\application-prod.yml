# 生产环境配置
server:
  port: 8233
  tomcat:
    basedir: /home/<USER>/prod

spring:
  datasource:
    druid:
      master:
        # 生产环境数据库配置
        url: *******************************************************************************************************************************************************
        username: smarthome_prod
        password: a4aSwZHaYpYGJTJw

  # 生产环境特定配置
  devtools:
    restart:
      enabled: false
  thymeleaf:
    cache: true

# 生产环境特定配置
ruoyi:
  profile: /home/<USER>/prod/file
  demoEnabled: false

logging:
  file:
    path: /home/<USER>/prod/logs
  level:
    com.ehome: debug
    org.springframework: debug
    # 统一SQL日志配置
    sql-log: info
    com.ehome.**.mapper: info
    org.apache.ibatis: info
    druid.sql.Statement: info
    druid.sql.DataSource: info
    com.jfinal.plugin.activerecord: info
    root: debug

# 功能开关
swagger:
  enabled: false

# Token配置 - 永不过期机制
token:
  expire-time: 2592000000   # 30天，极长有效期确保永不过期
  refresh-threshold: 604800000 # 7天，提前刷新确保连续性
  auto-refresh-enabled: true
  secret: ehomeProductionSecretKey2025!@#$%^&*
  force-logout-enabled: true           # 强制下线功能开关，默认关闭
  force-logout-date: "@build.date@"     # 强制下线日期，格式：yyyyMMdd，Maven打包时自动替换为打包日期

# 微信小程序配置
wechat:
  appid: wxf279bf8df3d4d470
  secret: a62d650089f3c5ed044c418cac11ab30

# 阿里云OSS配置 - 生产环境
aliyun:
  oss:
    # 生产环境路径前缀
    path-prefix: uploads/prod